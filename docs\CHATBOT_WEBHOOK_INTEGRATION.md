# Chatbot Webhook Integration

## Overview
This document describes the chatbot webhook integration implemented in the Smart Office Assistant application. The integration automatically triggers webhook notifications to the chatbot system when users authenticate, enabling personalized onboarding and ongoing support.

## Webhook URL
```
https://n8n.taqnik.in/webhook/a2e1a26e-d2bc-4e1c-b94a-cfb56f63489e
```

## Integration Points

### 1. Authentication Flow Integration
The webhook is triggered automatically in the following scenarios:
- **New user sign-in**: When a user signs in for the first time
- **Returning user sign-in**: When an existing user signs in
- **Session restoration**: When the app restores a user session on startup
- **Auth state changes**: When authentication state changes

### 2. Payload Structure
The webhook sends a POST request with the following JSON payload:

```json
{
  "first_time_user": boolean,
  "employee_details": {
    "full_name": string,
    "employee_id": string,
    "date_of_joining": string,
    "work_hours": string,
    "work_mode": string,
    "department": string,
    "phone_number": string,
    "location": string,
    "wfh_eligibility": boolean
  }
}
```

### 3. First-Time User Detection
The system determines first-time user status by:
- Checking the `is_first_time_user` field in the database
- Verifying if onboarding has been completed
- Fallback to `true` for users without complete profile data

### 4. Employee Data Mapping
The system maps database fields to webhook payload:
- `full_name` ← `employee_details.full_name`
- `employee_id` ← `employee_details.employee_id`
- `date_of_joining` ← `employee_details.date_of_joining`
- `work_hours` ← `employee_details.work_hours`
- `work_mode` ← Transformed from database values:
  - `'in-office'` → `'On-site'`
  - `'wfh'` → `'Remote'`
  - `'hybrid'` → `'Hybrid'`
- `department` ← `employee_details.department`
- `phone_number` ← `employee_details.phone_number`
- `location` ← `employee_details.location`
- `wfh_eligibility` ← `employee_details.wfh_eligibility` or derived from work_mode

## Implementation Files

### Core Service
- **`services/ChatbotWebhookService.ts`**: Main webhook service with retry logic
- **`types/ChatbotTypes.ts`**: TypeScript type definitions

### Integration Points
- **`AuthContext.tsx`**: Webhook triggers in authentication flow
- **`services/ConfigService.ts`**: Configuration management
- **`lib/supabase-api.ts`**: Database API extensions

### Testing
- **`components/ChatbotWebhookTest.tsx`**: Test component for webhook verification

## Configuration

### Environment Variables
```bash
EXPO_PUBLIC_CHATBOT_WEBHOOK_URL=https://n8n.taqnik.in/webhook/a2e1a26e-d2bc-4e1c-b94a-cfb56f63489e
EXPO_PUBLIC_CHATBOT_ENABLED=true
```

### Default Configuration
- **Timeout**: 30 seconds
- **Max Retry Attempts**: 3
- **Retry Delay**: 2 seconds (with exponential backoff)

## Error Handling

### Graceful Failure
- Webhook failures do not block user authentication
- Errors are logged but do not affect user experience
- Retry logic with exponential backoff for transient failures

### Logging
- Successful webhook calls are logged with timing information
- Failed attempts are logged with error details
- Network timeouts and connectivity issues are handled gracefully

## Testing

### Manual Testing
Use the `ChatbotWebhookTest` component to:
1. Test webhook connectivity
2. Send sample webhook payloads
3. Verify configuration settings

### Integration Testing
The webhook is automatically triggered during:
- User sign-in flow
- Session restoration
- Authentication state changes

## Security Considerations

### Data Privacy
- Only necessary employee information is sent to the webhook
- Sensitive data like passwords are never included
- User consent should be obtained for data sharing

### Network Security
- HTTPS-only webhook URL
- Request timeout to prevent hanging connections
- Retry limits to prevent excessive requests

## Monitoring

### Success Metrics
- Webhook success rate
- Response times
- Retry attempt frequency

### Error Monitoring
- Failed webhook attempts
- Network connectivity issues
- Configuration errors

## Troubleshooting

### Common Issues
1. **Webhook not triggered**: Check if `EXPO_PUBLIC_CHATBOT_ENABLED=true`
2. **Network timeouts**: Verify webhook URL accessibility
3. **Missing employee data**: Ensure user profile is complete
4. **Configuration errors**: Verify environment variables

### Debug Logging
Enable debug logging to see detailed webhook execution:
```bash
EXPO_PUBLIC_ENABLE_DEBUG_LOGGING=true
```

## Future Enhancements

### Potential Improvements
- Webhook authentication/signing
- Batch webhook processing
- Webhook event types (login, logout, profile update)
- User preference for webhook notifications
- Webhook delivery confirmation
